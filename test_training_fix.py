#!/usr/bin/env python3
"""
Test script to verify the training fix works
"""

import sys
import os
import numpy as np

# Add current directory to path
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

def test_training_fix():
    """Test the fixed training functionality"""
    print("Testing Training Fix...")
    
    try:
        from custom_gesture_manager import CustomGestureManager
        from feature_extractor import FeatureExtractor
        
        # Create manager and extractor
        manager = CustomGestureManager()
        extractor = FeatureExtractor()
        
        print("✓ Components created successfully")
        
        # Create a test gesture
        gesture_name = "test_training_gesture"
        success = manager.create_new_gesture(gesture_name, "Test gesture for training")
        
        if not success:
            print("✗ Failed to create test gesture")
            return False
        
        print("✓ Test gesture created")
        
        # Generate some dummy training samples
        print("Generating training samples...")
        for i in range(10):  # Create 10 samples
            # Create dummy landmarks with slight variations
            dummy_landmarks = []
            for j in range(21):
                landmark = type('Landmark', (), {})()
                landmark.x = 0.5 + 0.1 * np.sin(j) + 0.02 * np.random.randn()  # Add noise
                landmark.y = 0.5 + 0.1 * np.cos(j) + 0.02 * np.random.randn()  # Add noise
                dummy_landmarks.append(landmark)
            
            # Create dummy hand landmarks object
            hand_landmarks = type('HandLandmarks', (), {})()
            hand_landmarks.landmark = dummy_landmarks
            
            # Add sample
            success = manager.add_gesture_sample(gesture_name, hand_landmarks)
            if not success:
                print(f"✗ Failed to add sample {i+1}")
                return False
        
        print("✓ Training samples added")
        
        # Try training
        print("Training gesture...")
        success, accuracy = manager.train_gesture(gesture_name)
        
        if success:
            print(f"✓ Training successful! Accuracy: {accuracy:.2%}")
            
            # Test recognition
            print("Testing recognition...")
            
            # Create a test sample similar to training data
            test_landmarks = []
            for j in range(21):
                landmark = type('Landmark', (), {})()
                landmark.x = 0.5 + 0.1 * np.sin(j) + 0.01 * np.random.randn()
                landmark.y = 0.5 + 0.1 * np.cos(j) + 0.01 * np.random.randn()
                test_landmarks.append(landmark)
            
            test_hand_landmarks = type('HandLandmarks', (), {})()
            test_hand_landmarks.landmark = test_landmarks
            
            recognized_gesture, confidence = manager.recognize_gesture(test_hand_landmarks)
            
            if recognized_gesture == gesture_name:
                print(f"✓ Recognition successful! Confidence: {confidence:.2%}")
            else:
                print(f"⚠ Recognition returned: {recognized_gesture} (confidence: {confidence:.2%})")
            
        else:
            print("✗ Training failed")
            return False
        
        # Clean up
        manager.delete_gesture(gesture_name)
        print("✓ Test gesture cleaned up")
        
        return True
        
    except Exception as e:
        print(f"✗ Error: {e}")
        import traceback
        traceback.print_exc()
        return False

def main():
    """Run the test"""
    print("=" * 50)
    print("Training Fix Test")
    print("=" * 50)
    
    if test_training_fix():
        print("\n🎉 Training fix successful!")
        print("The custom gesture training should now work properly.")
    else:
        print("\n❌ Training fix failed!")
        print("There may still be issues with the training pipeline.")
    
    print("=" * 50)

if __name__ == "__main__":
    main()
