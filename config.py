"""
GestureFlow Configuration
EPIC GFLOW-E01: Core Gesture Recognition Engine

Configuration parameters for webcam capture, MediaPipe, and gesture recognition.
"""

# Webcam Configuration
WEBCAM_CONFIG = {
    'width': 640,
    'height': 480,
    'fps': 30,
    'device_id': 0,  # Default webcam
    'flip_horizontal': True  # Mirror effect
}

# MediaPipe Hands Configuration
MEDIAPIPE_CONFIG = {
    'static_image_mode': False,
    'max_num_hands': 2,
    'min_detection_confidence': 0.7,
    'min_tracking_confidence': 0.5
}

# Gesture Recognition Configuration
GESTURE_CONFIG = {
    'recognition_threshold': 0.8,  # Confidence threshold for gesture recognition
    'gesture_hold_time': 0.5,     # Minimum time to hold gesture (seconds)
    'smoothing_frames': 3,         # Number of frames for gesture smoothing
    'debug_mode': False            # Enable debug output for gesture recognition
}

# Performance Configuration (GFLOW-4)
PERFORMANCE_CONFIG = {
    'fps_update_interval': 30,     # Update FPS every N frames
    'max_recognition_history': 100, # Keep last N recognition times
    'target_fps': 15,              # Minimum target FPS
    'max_latency_ms': 50           # Maximum acceptable recognition latency
}

# UI Configuration
UI_CONFIG = {
    'window_title': 'GestureFlow - Core Gesture Recognition Engine',
    'window_width': 1000,
    'window_height': 700,
    'video_width': 640,
    'video_height': 480
}

# Custom Gesture Configuration (GFLOW-E02)
CUSTOM_GESTURE_CONFIG = {
    'data_directory': 'data/custom_gestures',
    'models_directory': 'data/models',
    'samples_per_gesture': 20,          # Number of samples to collect per gesture
    'sample_delay_seconds': 1.5,        # Time delay between samples (enhanced UX)
    'recording_countdown': 3,            # Countdown before recording starts
    'min_confidence_threshold': 0.7,    # Minimum confidence for recognition
    'feature_vector_size': 42,           # 21 landmarks × 2 coordinates (x,y)
    'similarity_threshold': 0.85,        # Threshold for gesture ambiguity detection
    'svm_kernel': 'rbf',                # SVM kernel type
    'svm_c': 1.0,                       # SVM regularization parameter
    'cross_validation_folds': 5,        # K-fold cross-validation
    'max_gesture_name_length': 50,      # Maximum length for gesture names
    'backup_enabled': True,             # Enable automatic backup of gesture data
}

# Gesture Definitions (GFLOW-3)
PREDEFINED_GESTURES = {
    'open_palm': {
        'name': 'Open Palm',
        'description': 'All fingers extended',
        'enabled': True
    },
    'fist': {
        'name': 'Fist',
        'description': 'All fingers closed',
        'enabled': True
    },
    'peace_sign': {
        'name': 'Peace Sign',
        'description': 'Index and middle fingers up',
        'enabled': True
    },
    'thumbs_up': {
        'name': 'Thumbs Up',
        'description': 'Thumb extended upward',
        'enabled': True
    },
    'pointing': {
        'name': 'Pointing',
        'description': 'Index finger extended',
        'enabled': True
    }
}
