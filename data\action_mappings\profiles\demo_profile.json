{"profile_name": "demo_profile", "created_date": "2025-05-31T10:44:43.594035", "mappings": {"open_palm": {"gesture_id": "open_palm", "gesture_name": "Open Palm", "gesture_type": "predefined", "action_type": "mouse_click", "parameters": {"button": "left", "clicks": 1}, "enabled": true, "created_date": "2025-05-31T10:44:41.680192", "last_used": null, "use_count": 0, "description": "Left click with open palm"}, "fist": {"gesture_id": "fist", "gesture_name": "Fist", "gesture_type": "predefined", "action_type": "hotkey", "parameters": {"keys": ["ctrl", "c"]}, "enabled": true, "created_date": "2025-05-31T10:44:41.681192", "last_used": null, "use_count": 0, "description": "Co<PERSON> with fist gesture"}, "peace_sign": {"gesture_id": "peace_sign", "gesture_name": "Peace Sign", "gesture_type": "predefined", "action_type": "type_text", "parameters": {"text": "Hello from GestureFlow! ✌️", "interval": 0.05}, "enabled": true, "created_date": "2025-05-31T10:44:41.682192", "last_used": null, "use_count": 0, "description": "Type greeting with peace sign"}, "thumbs_up": {"gesture_id": "thumbs_up", "gesture_name": "Thumbs Up", "gesture_type": "predefined", "action_type": "mouse_scroll", "parameters": {"direction": "up", "amount": 3}, "enabled": true, "created_date": "2025-05-31T10:44:41.683193", "last_used": null, "use_count": 0, "description": "Scroll up with thumbs up"}, "test_gesture": {"gesture_id": "test_gesture", "gesture_name": "Test Gesture", "gesture_type": "custom", "action_type": "type_text", "parameters": {"text": "Test Profile Action", "interval": 0.05}, "enabled": true, "created_date": "2025-05-31T10:44:43.593035", "last_used": null, "use_count": 0, "description": "Test mapping for demo profile"}}}